{"name": "kaeru", "version": "3.0.1", "description": "A Discord bot is doing unique things.", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "npm run build && node dist/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/minesa-org/kaeru.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "discordjs", "discord-bot", "moderation"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/minesa-org/kaeru/issues"}, "homepage": "https://github.com/minesa-org/kaeru#readme", "dependencies": {"@google/generative-ai": "^0.24.1", "@iamtraction/google-translate": "^2.0.1", "chalk": "^5.4.1", "discord.js": "^14.19.1", "dotenv": "^17.0.0", "express": "^5.1.0", "fs": "^0.0.1-security", "moment-timezone": "^0.6.0", "mongoose": "^8.10.1", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.4.2", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}