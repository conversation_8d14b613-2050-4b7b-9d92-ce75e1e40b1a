export const emojis = {
	// ticket
	ticket: "<:ticket:1331654460325498931>",
	ticketCreated: "<:ticket_created:1331655022357905419>",
	ticketDone: "<:done:1331654246264995891>",
	ticketStale: "<:stale:1331654403177971753>",
	ticketClose: "<:close:1331654220230819921>",
	ticketReopen: "<:reopen:1331654349754994789>",
	ticketLock: "<:lock:1331654306599800902>",
	ticketLockOpen: "<:key:1378356324680859688>",

	// memes & nsfw
	brain: "<:brain:1331654195215859823>",
	sensitive: "<:sensitive:1331654389680574546>",

	// discussion
	bubble: "<:bubble:1331654206540746863>",

	// information
	info: "<:react_Info:1353837616256122981>",
	error: "<:react_Error:1353837592919015567>",
	danger: "<:react_Danger:1353837568755765359>",
	redirect: "<:redirect:1331668579770306692>",

	// user
	avatar: "<:avatar:1331654153558167666>",
	banner: "<:banner:1331654167315349535>",

	// issue labels
	label: {
		bugLabel: "<:label_bug:1375482268109377536>",
		rewardLabel: "<:label_reward:1375482431251152956>",
		questionLabel: "<:label_question:1375482334232444988>",
		discussionLabel: "<:label_discussion:1375482386183356446>",
		helpLabel: "<:label_help:1375482474280255539>",
	},

	reactions: {
		// kaeru
		reaction_heart: "<:react_Heart:1353817513338601492>",
		reaction_thumbsup: "<:react_ThumbsUp:1353817554249977896>",
		reaction_thumbsdown: "<:react_ThumbsDown:1353817567814356992>",
		reaction_haha: "<:react_Haha:1353817585854189788>",
		reaction_emphasize: "<:react_Emphasize:1353817599690932336>",
		reaction_question: "<:react_Question:1353817612525637734>",

		// user
		reaction_heart_u: "<:reaction_heart_u:1375476629316567061>",
		reaction_thumbsup_u: "<:reaction_thumbsup_u:1375476669149876234>",
		reaction_thumbsdown_u: "<:reaction_thumbsdown_u:1375476654977319012>",
		reaction_haha_u: "<:reaction_haha_u:1375476616611889212>",
		reaction_emphasize_u: "<:reaction_emphasize_u:1375476602041012295>",
		reaction_question_u: "<:reaction_question_u:1375476642104999999>",
	},

	// other
	button: "<:button_press:1331655705039474772>",
	giftCard: "<:gift:1331654256691777657>",
	translate: "<:translate:1331654485096796300>",
	timeout: "<:timeout:1331654473008939070>",
	up: "<:up:1331654045869539359>",
	safety: "<:safety:1331654372744233040>",
	doorEnter: "<:door_enter:1331678912866156595>",
	swap: "<:swap:1331704952833179780>",
	globe: "<:globe:1331705467839320074>",
	dnd: "<:dnd:1378835964197076992>",

	// AI
	intelligence: "<:Karu:1375522815549116566>",
	magic: "<:wand:1375436613856788550>",
	text_append: "<:text_append:1375448596509495366>",
	list_bullet: "<:list_bullet:1375468308022951966>",
};

export type Emojis = typeof emojis;
