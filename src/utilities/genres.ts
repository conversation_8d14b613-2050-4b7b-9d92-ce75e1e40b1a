export interface GenreItem {
	artist: string;
	query: string;
}

export interface Genres {
	traditional_pop: GenreItem[];
	rock: GenreItem[];
	classical: GenreItem[];
	jazz: GenreItem[];
	electronic: GenreItem[];
	love_songs: GenreItem[];
	broken_heart: GenreItem[];
	chill: GenreItem[];
	metal: GenreItem[];
	hip_hop_rap: GenreItem[];
	rnb: GenreItem[];
	dance: GenreItem[];
	kpop: GenreItem[];
}

export const genres: Genres = {
	traditional_pop: [
		{ artist: "<PERSON>", query: "<PERSON>" },
		{ artist: "<PERSON><PERSON><PERSON>", query: "<PERSON><PERSON><PERSON>" },
		{ artist: "<PERSON><PERSON><PERSON>", query: "<PERSON><PERSON><PERSON>" },
		{ artist: "<PERSON><PERSON>u", query: "<PERSON><PERSON> Aksu <PERSON>" },
		{ artist: "Nil<PERSON><PERSON>", query: "<PERSON><PERSON><PERSON><PERSON>" },
		{ artist: "<PERSON>", query: "<PERSON>" },
		{ artist: "<PERSON><PERSON>", query: "ABBA Dancing Queen" },
		{ artist: "The Beatles", query: "The Beatles Hey Jude" },
		{ artist: "<PERSON><PERSON>", query: "<PERSON><PERSON> Dion My Heart Will Go On" },
		{ artist: "<PERSON> <PERSON>", query: "<PERSON> <PERSON>" },
		{ artist: "<PERSON> I<PERSON>", query: "<PERSON> I<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>" },
		{ artist: "<PERSON> Pa<PERSON><PERSON>", query: "<PERSON> <PERSON><PERSON><PERSON> <PERSON> <PERSON>it<PERSON><PERSON>" },
		{ artist: "<PERSON> <PERSON>s", query: "<PERSON> <PERSON><PERSON> <PERSON><PERSON>' Alive" },
		{
			artist: "<PERSON>e <PERSON>",
			query: "<PERSON>e <PERSON> Atem<PERSON> du<PERSON> die Na<PERSON>",
		},
		{ artist: "Björk", query: "Björk It's Oh So Quiet" },
		{ artist: "Shakira", query: "Shakira Hips Don't Lie" },
		{ artist: "Seal", query: "Seal Kiss from a Rose" },
		{ artist: "Khaled", query: "Khaled Aïcha" },
		{ artist: "Eros Ramazzotti", query: "Eros Ramazzotti Più Bella Cosa" },
		{ artist: "Zaz", query: "Zaz Je Veux" },
	],
	rock: [
		{ artist: "Queen", query: "Queen Bohemian Rhapsody" },
		{ artist: "Led Zeppelin", query: "Led Zeppelin Stairway to Heaven" },
		{ artist: "Pink Floyd", query: "Pink Floyd Another Brick in the Wall" },
		{ artist: "The Beatles", query: "The Beatles Hey Jude" },
		{ artist: "AC/DC", query: "AC/DC Highway to Hell" },
		{ artist: "Guns N' Roses", query: "Guns N' Roses Sweet Child O' Mine" },
		{
			artist: "The Rolling Stones",
			query: "The Rolling Stones Paint It Black",
		},
		{ artist: "Nirvana", query: "Nirvana Smells Like Teen Spirit" },
		{ artist: "U2", query: "U2 With or Without You" },
		{ artist: "Deep Purple", query: "Deep Purple Smoke on the Water" },
		{ artist: "Bon Jovi", query: "Bon Jovi Livin' on a Prayer" },
		{ artist: "The Who", query: "The Who Baba O'Riley" },
		{ artist: "Foo Fighters", query: "Foo Fighters Everlong" },
		{
			artist: "Red Hot Chili Peppers",
			query: "Red Hot Chili Peppers Californication",
		},
		{ artist: "Pearl Jam", query: "Pearl Jam Alive" },
		{ artist: "The Doors", query: "The Doors Light My Fire" },
		{ artist: "Radiohead", query: "Radiohead Creep" },
		{ artist: "The Clash", query: "The Clash London Calling" },
		{ artist: "Scorpions", query: "Scorpions Wind of Change" },
		{ artist: "Eagles", query: "Eagles Hotel California" },
	],
	classical: [
		{
			artist: "Johann Sebastian Bach",
			query: "Bach - Toccata and Fugue in D minor",
		},
		{
			artist: "Ludwig van Beethoven",
			query: "Beethoven - Symphony No. 9 'Ode to Joy'",
		},
		{
			artist: "Wolfgang Amadeus Mozart",
			query: "Mozart - Eine kleine Nachtmusik",
		},
		{
			artist: "Pyotr Ilyich Tchaikovsky",
			query: "Tchaikovsky - Swan Lake",
		},
		{
			artist: "Frédéric Chopin",
			query: "Chopin - Nocturne in E-flat Major",
		},
		{ artist: "Antonio Vivaldi", query: "Vivaldi - The Four Seasons" },
		{ artist: "Claude Debussy", query: "Debussy - Clair de Lune" },
		{ artist: "Johannes Brahms", query: "Brahms - Symphony No. 4" },
		{ artist: "Giuseppe Verdi", query: "Verdi - La Traviata" },
		{ artist: "Franz Schubert", query: "Schubert - Ave Maria" },
		{ artist: "Igor Stravinsky", query: "Stravinsky - The Rite of Spring" },
		{ artist: "Gustav Mahler", query: "Mahler - Symphony No. 5" },
		{
			artist: "Camille Saint-Saëns",
			query: "Saint-Saëns - The Carnival of the Animals",
		},
		{ artist: "Richard Wagner", query: "Wagner - Ride of the Valkyries" },
		{ artist: "Maurice Ravel", query: "Ravel - Boléro" },
		{
			artist: "Dmitri Shostakovich",
			query: "Shostakovich - Symphony No. 5",
		},
		{
			artist: "Gioachino Rossini",
			query: "Rossini - William Tell Overture",
		},
		{
			artist: "Sergei Rachmaninoff",
			query: "Rachmaninoff - Piano Concerto No. 2",
		},
		{ artist: "Franz Liszt", query: "Liszt - Hungarian Rhapsody No. 2" },
		{ artist: "Carl Orff", query: "Orff - Carmina Burana" },
	],
	jazz: [
		{
			artist: "Louis Armstrong",
			query: "Louis Armstrong What a Wonderful World",
		},
		{ artist: "Ella Fitzgerald", query: "Ella Fitzgerald Summertime" },
		{
			artist: "Duke Ellington",
			query: "Duke Ellington Take the 'A' Train",
		},
		{ artist: "Miles Davis", query: "Miles Davis So What" },
		{ artist: "John Coltrane", query: "John Coltrane Giant Steps" },
		{ artist: "Charlie Parker", query: "Charlie Parker Yardbird Suite" },
		{ artist: "Thelonious Monk", query: "Thelonious Monk Round Midnight" },
		{ artist: "Billie Holiday", query: "Billie Holiday Strange Fruit" },
		{ artist: "Chet Baker", query: "Chet Baker My Funny Valentine" },
		{ artist: "Stan Getz", query: "Stan Getz The Girl from Ipanema" },
		{ artist: "Nina Simone", query: "Nina Simone Feeling Good" },
		{ artist: "Louis Prima", query: "Louis Prima Sing, Sing, Sing" },
		{ artist: "Dave Brubeck", query: "Dave Brubeck Take Five" },
		{ artist: "Herbie Hancock", query: "Herbie Hancock Watermelon Man" },
		{ artist: "Sarah Vaughan", query: "Sarah Vaughan Misty" },
		{ artist: "Count Basie", query: "Count Basie April in Paris" },
		{ artist: "Art Blakey", query: "Art Blakey Moanin'" },
		{
			artist: "Erich Kleinschuster",
			query: "Erich Kleinschuster Blues für Fredi",
		},
		{ artist: "Django Reinhardt", query: "Django Reinhardt Minor Swing" },
		{ artist: "Pat Metheny", query: "Pat Metheny Group Last Train Home" },
	],
	electronic: [
		{ artist: "Daft Punk", query: "Daft Punk Get Lucky" },
		{ artist: "Avicii", query: "Avicii Wake Me Up" },
		{ artist: "Calvin Harris", query: "Calvin Harris Summer" },
		{ artist: "David Guetta", query: "David Guetta Titanium" },
		{
			artist: "The Chemical Brothers",
			query: "The Chemical Brothers Block Rockin' Beats",
		},
		{ artist: "Deadmau5", query: "Deadmau5 Strobe" },
		{
			artist: "Skrillex",
			query: "Skrillex Scary Monsters and Nice Sprites",
		},
		{ artist: "Tiesto", query: "Tiesto Adagio for Strings" },
		{ artist: "Disclosure", query: "Disclosure Latch" },
		{ artist: "Martin Garrix", query: "Martin Garrix Animals" },
		{ artist: "Kraftwerk", query: "Kraftwerk The Model" },
		{ artist: "Fatboy Slim", query: "Fatboy Slim Praise You" },
		{ artist: "The Prodigy", query: "The Prodigy Firestarter" },
		{ artist: "Aphex Twin", query: "Aphex Twin Windowlicker" },
		{ artist: "Jean-Michel Jarre", query: "Jean-Michel Jarre Oxygene" },
		{ artist: "Underworld", query: "Underworld Born Slippy .NUXX" },
		{ artist: "Porter Robinson", query: "Porter Robinson Language" },
		{ artist: "Moby", query: "Moby Porcelain" },
		{
			artist: "Armin van Buuren",
			query: "Armin van Buuren In and Out of Love",
		},
		{ artist: "Justice", query: "Justice D.A.N.C.E." },
	],
	love_songs: [
		{ artist: "Ed Sheeran", query: "Ed Sheeran Perfect" },
		{ artist: "Adele", query: "Adele Someone Like You" },
		{ artist: "John Legend", query: "John Legend All of Me" },
		{
			artist: "Elvis Presley",
			query: "Elvis Presley Can't Help Falling in Love",
		},
		{
			artist: "Whitney Houston",
			query: "Whitney Houston I Will Always Love You",
		},
		{ artist: "Bruno Mars", query: "Bruno Mars Just the Way You Are" },
		{ artist: "The Beatles", query: "The Beatles Something" },
		{ artist: "Celine Dion", query: "Celine Dion My Heart Will Go On" },
		{ artist: "Shania Twain", query: "Shania Twain You're Still the One" },
		{ artist: "Jason Mraz", query: "Jason Mraz I'm Yours" },
		{ artist: "Norah Jones", query: "Norah Jones Come Away With Me" },
		{ artist: "Eric Clapton", query: "Eric Clapton Wonderful Tonight" },
		{ artist: "Beyoncé", query: "Beyoncé Halo" },
		{ artist: "Lionel Richie", query: "Lionel Richie Endless Love" },
		{ artist: "Taylor Swift", query: "Taylor Swift Love Story" },
		{ artist: "Michael Bublé", query: "Michael Bublé Everything" },
		{ artist: "Stevie Wonder", query: "Stevie Wonder Isn't She Lovely" },
		{
			artist: "Christina Perri",
			query: "Christina Perri A Thousand Years",
		},
		{
			artist: "Barry White",
			query: "Barry White You're the First, the Last, My Everything",
		},
		{ artist: "Mariah Carey", query: "Mariah Carey Always Be My Baby" },
	],
	broken_heart: [
		{ artist: "Adele", query: "Adele Someone Like You" },
		{ artist: "Taylor Swift", query: "Taylor Swift All Too Well" },
		{ artist: "Sam Smith", query: "Sam Smith Stay With Me" },
		{
			artist: "Bonnie Raitt",
			query: "Bonnie Raitt I Can't Make You Love Me",
		},
		{
			artist: "Sinead O'Connor",
			query: "Sinead O'Connor Nothing Compares 2 U",
		},
		{ artist: "Coldplay", query: "Coldplay The Scientist" },
		{ artist: "Alicia Keys", query: "Alicia Keys If I Ain't Got You" },
		{ artist: "Chris Isaak", query: "Chris Isaak Wicked Game" },
		{
			artist: "Alanis Morissette",
			query: "Alanis Morissette You Oughta Know",
		},
		{ artist: "James Blunt", query: "James Blunt Goodbye My Lover" },
		{ artist: "Goo Goo Dolls", query: "Goo Goo Dolls Iris" },
		{ artist: "Maroon 5", query: "Maroon 5 She Will Be Loved" },
		{
			artist: "Elton John",
			query: "Elton John Sorry Seems to Be the Hardest Word",
		},
		{ artist: "Evanescence", query: "Evanescence My Immortal" },
		{
			artist: "Roberta Flack",
			query: "Roberta Flack Killing Me Softly With His Song",
		},
		{
			artist: "Whitney Houston",
			query: "Whitney Houston I Will Always Love You",
		},
		{ artist: "Gotye", query: "Gotye Somebody That I Used to Know" },
		{ artist: "The Police", query: "The Police Every Breath You Take" },
		{ artist: "U2", query: "U2 With or Without You" },
		{ artist: "Fleetwood Mac", query: "Fleetwood Mac Go Your Own Way" },
	],
	chill: [
		{ artist: "Tycho", query: "Tycho Awake" },
		{ artist: "Bonobo", query: "Bonobo Kerala" },
		{ artist: "Odesza", query: "Odesza A Moment Apart" },
		{ artist: "Moby", query: "Moby Porcelain" },
		{ artist: "Zero 7", query: "Zero 7 In the Waiting Line" },
		{ artist: "Air", query: "Air La Femme d'Argent" },
		{ artist: "Massive Attack", query: "Massive Attack Teardrop" },
		{
			artist: "Thievery Corporation",
			query: "Thievery Corporation Lebanese Blonde",
		},
		{
			artist: "Emancipator",
			query: "Emancipator Soon It Will Be Cold Enough",
		},
		{ artist: "Chet Faker", query: "Chet Faker Gold" },
		{ artist: "Nujabes", query: "Nujabes Aruarian Dance" },
		{ artist: "Bibio", query: "Bibio Lover's Carvings" },
		{ artist: "Röyksopp", query: "Röyksopp Eple" },
		{ artist: "Little Dragon", query: "Little Dragon Twice" },
		{ artist: "Portishead", query: "Portishead Roads" },
		{ artist: "Four Tet", query: "Four Tet Love Cry" },
		{
			artist: "Cigarettes After Sex",
			query: "Cigarettes After Sex Apocalypse",
		},
		{ artist: "Nightmares on Wax", query: "Nightmares on Wax Les Nuits" },
		{ artist: "Gorillaz", query: "Gorillaz Melancholy Hill" },
		{ artist: "Helios", query: "Helios Halving the Compass" },
	],
	metal: [
		{ artist: "Metallica", query: "Metallica Enter Sandman" },
		{ artist: "Iron Maiden", query: "Iron Maiden The Number of the Beast" },
		{ artist: "Black Sabbath", query: "Black Sabbath Paranoid" },
		{ artist: "Slayer", query: "Slayer Raining Blood" },
		{ artist: "Megadeth", query: "Megadeth Symphony of Destruction" },
		{ artist: "Pantera", query: "Pantera Walk" },
		{ artist: "Judas Priest", query: "Judas Priest Painkiller" },
		{ artist: "System of a Down", query: "System of a Down Chop Suey!" },
		{ artist: "Slipknot", query: "Slipknot Psychosocial" },
		{ artist: "Tool", query: "Tool Schism" },
		{ artist: "Rammstein", query: "Rammstein Du Hast" },
		{ artist: "Motorhead", query: "Motorhead Ace of Spades" },
		{ artist: "Nightwish", query: "Nightwish Nemo" },
		{ artist: "Opeth", query: "Opeth Ghost of Perdition" },
		{ artist: "Meshuggah", query: "Meshuggah Bleed" },
		{ artist: "Avenged Sevenfold", query: "Avenged Sevenfold Bat" },
		{ artist: "Korn", query: "Korn Freak on a Leash" },
		{ artist: "Anthrax", query: "Anthrax Madhouse" },
		{ artist: "Mastodon", query: "Mastodon Blood and Thunder" },
		{ artist: "Arch Enemy", query: "Arch Enemy Nemesis" },
	],
	hip_hop_rap: [
		{ artist: "Eminem", query: "Eminem Lose Yourself" },
		{ artist: "Dr. Dre", query: "Dr. Dre Still D.R.E. (feat. Snoop Dogg)" },
		{ artist: "2Pac", query: "2Pac California Love" },
		{ artist: "Notorious B.I.G.", query: "Notorious B.I.G. Juicy" },
		{
			artist: "Jay-Z",
			query: "Jay-Z Empire State of Mind (feat. Alicia Keys)",
		},
		{ artist: "Kendrick Lamar", query: "Kendrick Lamar HUMBLE." },
		{ artist: "Nas", query: "Nas NY State of Mind" },
		{ artist: "Kanye West", query: "Kanye West Stronger" },
		{ artist: "OutKast", query: "OutKast Ms. Jackson" },
		{ artist: "50 Cent", query: "50 Cent In Da Club" },
		{ artist: "Snoop Dogg", query: "Snoop Dogg Gin and Juice" },
		{ artist: "Lil Wayne", query: "Lil Wayne A Milli" },
		{ artist: "Ice Cube", query: "Ice Cube It Was a Good Day" },
		{ artist: "Run-D.M.C.", query: "Run-D.M.C. Walk This Way" },
		{ artist: "Public Enemy", query: "Public Enemy Fight the Power" },
		{
			artist: "The Notorious B.I.G.",
			query: "The Notorious B.I.G. Big Poppa",
		},
		{
			artist: "A Tribe Called Quest",
			query: "A Tribe Called Quest Can I Kick It?",
		},
		{ artist: "Wu-Tang Clan", query: "Wu-Tang Clan C.R.E.A.M." },
		{ artist: "DMX", query: "DMX Party Up (Up in Here)" },
		{
			artist: "Childish Gambino",
			query: "Childish Gambino This Is America",
		},
	],
	rnb: [
		{ artist: "Beyoncé", query: "Beyoncé Irreplaceable" },
		{ artist: "Rihanna", query: "Rihanna Umbrella (feat. Jay-Z)" },
		{ artist: "Alicia Keys", query: "Alicia Keys No One" },
		{ artist: "Usher", query: "Usher Yeah! (feat. Lil Jon & Ludacris)" },
		{ artist: "Mariah Carey", query: "Mariah Carey We Belong Together" },
		{ artist: "Chris Brown", query: "Chris Brown With You" },
		{ artist: "Ne-Yo", query: "Ne-Yo So Sick" },
		{ artist: "TLC", query: "TLC No Scrubs" },
		{ artist: "Destiny's Child", query: "Destiny's Child Say My Name" },
		{ artist: "Boyz II Men", query: "Boyz II Men End of the Road" },
		{ artist: "Bruno Mars", query: "Bruno Mars Versace on the Floor" },
		{
			artist: "Whitney Houston",
			query: "Whitney Houston I Will Always Love You",
		},
		{ artist: "Michael Jackson", query: "Michael Jackson Rock with You" },
		{ artist: "Janet Jackson", query: "Janet Jackson Together Again" },
		{ artist: "Mary J. Blige", query: "Mary J. Blige Family Affair" },
		{ artist: "D'Angelo", query: "D'Angelo Untitled (How Does It Feel)" },
		{ artist: "R. Kelly", query: "R. Kelly Ignition (Remix)" },
		{ artist: "The Weeknd", query: "The Weeknd Blinding Lights" },
		{ artist: "Lauryn Hill", query: "Lauryn Hill Doo Wop (That Thing)" },
		{ artist: "Frank Ocean", query: "Frank Ocean Thinkin Bout You" },
	],
	dance: [
		{ artist: "Calvin Harris", query: "Calvin Harris Summer" },
		{ artist: "David Guetta", query: "David Guetta Titanium (feat. Sia)" },
		{ artist: "Avicii", query: "Avicii Wake Me Up" },
		{ artist: "Martin Garrix", query: "Martin Garrix Animals" },
		{
			artist: "Swedish House Mafia",
			query: "Swedish House Mafia Don't You Worry Child",
		},
		{ artist: "Zedd", query: "Zedd Clarity (feat. Foxes)" },
		{
			artist: "The Chainsmokers",
			query: "The Chainsmokers Closer (feat. Halsey)",
		},
		{ artist: "Marshmello", query: "Marshmello Alone" },
		{ artist: "Kygo", query: "Kygo Firestone (feat. Conrad Sewell)" },
		{
			artist: "Daft Punk",
			query: "Daft Punk Get Lucky (feat. Pharrell Williams)",
		},
		{ artist: "Disclosure", query: "Disclosure Latch (feat. Sam Smith)" },
		{
			artist: "Clean Bandit",
			query: "Clean Bandit Rather Be (feat. Jess Glynne)",
		},
		{ artist: "Dua Lipa", query: "Dua Lipa Don't Start Now" },
		{
			artist: "Robin Schulz",
			query: "Robin Schulz Prayer in C (Robin Schulz Remix)",
		},
		{ artist: "Galantis", query: "Galantis Runaway (U & I)" },
		{ artist: "Alan Walker", query: "Alan Walker Faded" },
		{ artist: "Tiesto", query: "Tiesto Red Lights" },
		{
			artist: "Alesso",
			query: "Alesso Heroes (we could be) [feat. Tove Lo]",
		},
		{ artist: "Steve Aoki", query: "Steve Aoki Boneless" },
		{
			artist: "Axwell Λ Ingrosso",
			query: "Axwell Λ Ingrosso More Than You Know",
		},
	],
	kpop: [
		{ artist: "BTS", query: "BTS Dynamite" },
		{ artist: "BLACKPINK", query: "BLACKPINK DDU-DU DDU-DU" },
		{ artist: "TWICE", query: "TWICE Fancy" },
		{ artist: "EXO", query: "EXO Love Shot" },
		{ artist: "Red Velvet", query: "Red Velvet Psycho" },
		{ artist: "GOT7", query: "GOT7 Lullaby" },
		{ artist: "Stray Kids", query: "Stray Kids God's Menu" },
		{ artist: "ITZY", query: "ITZY WANNABE" },
		{ artist: "MONSTA X", query: "MONSTA X Fantasia" },
		{ artist: "NCT", query: "NCT 127 Kick It" },
		{ artist: "SEVENTEEN", query: "SEVENTEEN HOME;RUN" },
		{ artist: "IU", query: "IU Eight (feat. SUGA)" },
		{ artist: "ATEEZ", query: "ATEEZ Say My Name" },
		{ artist: "MAMAMOO", query: "MAMAMOO HIP" },
		{ artist: "TXT", query: "TXT Blue Hour" },
		{ artist: "BTOB", query: "BTOB Beautiful Pain" },
		{ artist: "Everglow", query: "Everglow La Di Da" },
		{ artist: "OH MY GIRL", query: "OH MY GIRL Dolphin" },
		{ artist: "KARD", query: "KARD Dumb Litty" },
		{ artist: "PENTAGON", query: "PENTAGON Shine" },
	],
};
